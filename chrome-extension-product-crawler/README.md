# Product Crawler Chrome Extension

A Chrome extension for extracting product information from e-commerce marketplaces (Etsy, eBay, Amazon) and integrating with TikTok Shop.

## Features

### Phase 1 (Current Implementation)
- ✅ Manual product extraction from supported marketplaces
- ✅ Authentication with backend API
- ✅ Product data storage and management
- ✅ Basic popup interface
- ✅ Content scripts for Etsy, eBay, and Amazon

### Supported Marketplaces
- **Etsy**: Product listings (`/listing/*`)
- **eBay**: Item pages (`/itm/*`, `/p/*`)
- **Amazon**: Product pages (`/dp/*`, `/gp/product/*`)

## Installation (Development)

1. **Load Extension in Chrome**:
   - Open Chrome and navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top right)
   - Click "Load unpacked"
   - Select the `chrome-extension-product-crawler` directory

2. **Backend Setup**:
   - Ensure the NestJS backend is running on `http://localhost:3001`
   - Ensure the Next.js frontend is running on `http://localhost:3000`

3. **Icons** (Optional for development):
   - Add icon files to the `icons/` directory
   - Or use placeholder icons for testing

## Usage

### Manual Product Extraction

1. **Login**:
   - Click the extension icon in Chrome toolbar
   - Click "Login" button
   - Complete authentication in the opened web application tab

2. **Extract Products**:
   - Navigate to a product page on Etsy, eBay, or Amazon
   - Click the extension icon
   - Click "Extract Product" button
   - Product data will be saved to your account

3. **Manage Data**:
   - Use "View Crawled Products" to see extracted products
   - Use "Manage Schedules" for automated crawling (Phase 4)

## Architecture

### Content Scripts
- `common-extractor.js`: Shared utilities for data extraction
- `etsy-extractor.js`: Etsy-specific product extraction
- `ebay-extractor.js`: eBay-specific product extraction  
- `amazon-extractor.js`: Amazon-specific product extraction

### Background Scripts
- `service-worker.js`: Main background service worker
- `api-client.js`: Backend API communication
- `scheduler.js`: Automated crawling scheduler (Phase 4)

### Popup Interface
- `popup.html`: Extension popup UI
- `popup.css`: Popup styling
- `popup.js`: Popup functionality and user interactions

## Data Extraction

### Extracted Fields
- **Title**: Product name/title
- **URL**: Direct link to product page
- **Marketplace**: Source platform (etsy/ebay/amazon)
- **Seller**: Shop/seller name
- **Images**: Product images (converted to full resolution)
- **Metadata**: Price, rating, reviews, availability, description

### Image Processing
- Automatically converts thumbnail URLs to full-resolution images
- Stores original URLs and processes them on the backend
- Uploads to Cloudflare R2 storage

## API Integration

### Authentication
- Uses JWT tokens for API authentication
- Integrates with existing NextAuth session management
- Supports token refresh and logout

### Endpoints Used
- `POST /crawled-products` - Store extracted product data
- `GET /crawled-products` - Retrieve user's crawled products
- `GET /auth/verify` - Verify authentication status
- `POST /auth/logout` - Logout user

## Development

### File Structure
```
chrome-extension-product-crawler/
├── manifest.json              # Extension manifest
├── background/               # Background scripts
│   ├── service-worker.js    # Main service worker
│   ├── api-client.js        # API communication
│   └── scheduler.js         # Crawling scheduler
├── content-scripts/         # Content scripts
│   ├── common-extractor.js  # Shared utilities
│   ├── etsy-extractor.js    # Etsy extraction
│   ├── ebay-extractor.js    # eBay extraction
│   └── amazon-extractor.js  # Amazon extraction
├── popup/                   # Popup interface
│   ├── popup.html          # Popup HTML
│   ├── popup.css           # Popup styles
│   └── popup.js            # Popup logic
└── icons/                  # Extension icons
    └── README.md           # Icon requirements
```

### Adding New Marketplaces

1. **Create Content Script**:
   - Add new extractor file (e.g., `shopify-extractor.js`)
   - Implement marketplace-specific selectors
   - Follow existing extractor patterns

2. **Update Manifest**:
   - Add content script entry for new marketplace
   - Add host permissions for the domain

3. **Update Detection Logic**:
   - Add marketplace detection in popup and background scripts
   - Update UI to show new marketplace badge

## Future Phases

### Phase 2: Core Extraction Functionality
- Enhanced error handling and retry logic
- Better image quality detection and processing
- Improved data validation and cleaning

### Phase 3: Authentication & Web Application Integration  
- Enhanced authentication flow
- Web application pages for managing crawled data
- React components for data visualization

### Phase 4: Automated Crawling & Advanced Features
- Scheduled keyword-based crawling
- Anti-detection measures and rate limiting
- Bulk product processing and conversion

### Phase 5: Testing, Optimization & Deployment
- Comprehensive testing suite
- Chrome Web Store preparation
- Production deployment and monitoring

## Security Considerations

- No sensitive data stored in extension storage
- All API communication uses HTTPS
- Authentication tokens are securely managed
- Content scripts run in isolated contexts
- Follows Chrome extension security best practices

## Troubleshooting

### Common Issues

1. **Authentication Failed**:
   - Ensure backend is running and accessible
   - Check browser console for network errors
   - Try logging out and logging back in

2. **Extraction Not Working**:
   - Verify you're on a supported product page
   - Check if marketplace selectors need updating
   - Look for console errors in developer tools

3. **Extension Not Loading**:
   - Verify manifest.json syntax
   - Check for JavaScript errors in background script
   - Ensure all required permissions are granted

### Debug Mode
- Open Chrome DevTools on extension popup
- Check background script logs in `chrome://extensions/`
- Monitor network requests in DevTools Network tab
