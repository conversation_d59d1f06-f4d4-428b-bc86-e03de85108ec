// API client for communicating with the backend server

export class ApiClient {
  constructor() {
    // TODO: Replace with actual backend URL
    this.baseUrl = 'http://localhost:3001'; // NestJS backend URL
    this.webAppUrl = 'http://localhost:3000'; // Next.js frontend URL
  }

  async makeRequest(endpoint, options = {}) {
    try {
      const token = await this.getAuthToken();
      
      const defaultOptions = {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        }
      };

      const requestOptions = {
        ...defaultOptions,
        ...options,
        headers: {
          ...defaultOptions.headers,
          ...options.headers
        }
      };

      const response = await fetch(`${this.baseUrl}${endpoint}`, requestOptions);
      
      if (response.status === 401) {
        // Token expired or invalid
        await this.clearAuthToken();
        throw new Error('Authentication required');
      }

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || `HTTP ${response.status}`);
      }

      return { success: true, data };
    } catch (error) {
      console.error('API request failed:', error);
      return { success: false, error: error.message };
    }
  }

  async getAuthToken() {
    const result = await chrome.storage.local.get(['authToken']);
    return result.authToken;
  }

  async setAuthToken(token) {
    await chrome.storage.local.set({ 
      authToken: token,
      isAuthenticated: true 
    });
  }

  async clearAuthToken() {
    await chrome.storage.local.remove(['authToken']);
    await chrome.storage.local.set({ isAuthenticated: false });
  }

  async checkAuthStatus() {
    const token = await this.getAuthToken();
    
    if (!token) {
      return { isAuthenticated: false };
    }

    // Verify token with backend
    const result = await this.makeRequest('/auth/verify');
    
    if (result.success) {
      return { 
        isAuthenticated: true, 
        user: result.data 
      };
    } else {
      await this.clearAuthToken();
      return { isAuthenticated: false };
    }
  }

  async getLoginUrl() {
    // Return the web application login URL
    return `${this.webAppUrl}/auth/signin?source=extension`;
  }

  async logout() {
    const result = await this.makeRequest('/auth/logout', {
      method: 'POST'
    });
    
    await this.clearAuthToken();
    return result;
  }

  async createCrawledProduct(productData) {
    return this.makeRequest('/crawled-products', {
      method: 'POST',
      body: JSON.stringify(productData)
    });
  }

  async getCrawledProducts(query = {}) {
    const queryString = new URLSearchParams(query).toString();
    const endpoint = `/crawled-products${queryString ? `?${queryString}` : ''}`;
    
    return this.makeRequest(endpoint);
  }

  async getCrawlSchedules() {
    return this.makeRequest('/crawl-schedules');
  }

  async createCrawlSchedule(scheduleData) {
    return this.makeRequest('/crawl-schedules', {
      method: 'POST',
      body: JSON.stringify(scheduleData)
    });
  }

  async updateCrawlSchedule(scheduleId, scheduleData) {
    return this.makeRequest(`/crawl-schedules/${scheduleId}`, {
      method: 'PUT',
      body: JSON.stringify(scheduleData)
    });
  }

  async deleteCrawlSchedule(scheduleId) {
    return this.makeRequest(`/crawl-schedules/${scheduleId}`, {
      method: 'DELETE'
    });
  }

  async toggleCrawlSchedule(scheduleId) {
    return this.makeRequest(`/crawl-schedules/${scheduleId}/toggle`, {
      method: 'POST'
    });
  }

  // Method to handle authentication from web app
  async handleAuthCallback(token) {
    if (token) {
      await this.setAuthToken(token);
      return { success: true };
    } else {
      return { success: false, error: 'No token provided' };
    }
  }

  // Method to get user profile
  async getUserProfile() {
    return this.makeRequest('/auth/profile');
  }

  // Method to refresh auth token
  async refreshToken() {
    const result = await this.makeRequest('/auth/refresh', {
      method: 'POST'
    });
    
    if (result.success && result.data.accessToken) {
      await this.setAuthToken(result.data.accessToken);
    }
    
    return result;
  }
}
