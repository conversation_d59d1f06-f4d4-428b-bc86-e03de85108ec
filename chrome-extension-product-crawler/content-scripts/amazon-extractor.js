// Amazon-specific product data extractor

window.AmazonExtractor = {
  // Amazon-specific selectors
  selectors: {
    title: [
      '#productTitle',
      '.product-title',
      'h1.a-size-large'
    ],
    images: [
      '#landingImage',
      '.a-dynamic-image',
      '#imgTagWrapperId img',
      '.image-wrapper img',
      '.a-button-thumbnail img'
    ],
    seller: [
      '#sellerProfileTriggerId',
      '.a-link-normal[href*="/seller/"]',
      '#merchant-info a',
      '.tabular-buybox-text[tabular-attribute-name="Sold by"] span'
    ],
    price: [
      '.a-price-whole',
      '.a-price .a-offscreen',
      '#priceblock_dealprice',
      '#priceblock_ourprice',
      '.a-price-range .a-price .a-offscreen'
    ],
    rating: [
      '.a-icon-alt',
      '[data-hook="average-star-rating"] .a-icon-alt',
      '.reviewCountTextLinkedHistogram .a-icon-alt'
    ],
    reviewCount: [
      '#acrCustomerReviewText',
      '[data-hook="total-review-count"]',
      '.reviewCountTextLinkedHistogram .a-size-base'
    ],
    availability: [
      '#availability span',
      '.a-color-success',
      '.a-color-state',
      '#merchant-info'
    ],
    description: [
      '#feature-bullets ul',
      '.a-unordered-list.a-vertical .a-list-item',
      '#productDescription p'
    ]
  },

  // Extract product data from Amazon product page
  extractProductData() {
    try {
      console.log('Extracting Amazon product data...');

      const productData = {
        title: this.extractTitle(),
        productUrl: CommonExtractor.getCurrentUrl(),
        marketplace: 'amazon',
        sellerName: this.extractSeller(),
        images: this.extractImages(),
        metadata: this.extractMetadata()
      };

      // Validate the extracted data
      const validation = CommonExtractor.validateProductData(productData);
      
      if (!validation.isValid) {
        console.error('Amazon product data validation failed:', validation.errors);
        CommonExtractor.showNotification(
          'Failed to extract complete product data: ' + validation.errors.join(', '),
          'error'
        );
        return null;
      }

      console.log('Amazon product data extracted successfully:', productData);
      return productData;

    } catch (error) {
      console.error('Error extracting Amazon product data:', error);
      CommonExtractor.showNotification('Error extracting product data: ' + error.message, 'error');
      return null;
    }
  },

  extractTitle() {
    for (const selector of this.selectors.title) {
      const title = CommonExtractor.extractText(selector);
      if (title && title.length > 5) {
        return CommonExtractor.cleanText(title);
      }
    }
    
    // Fallback to page title
    const pageTitle = document.title;
    if (pageTitle && !pageTitle.includes('Amazon')) {
      return CommonExtractor.cleanText(pageTitle.split(':')[0]);
    }
    
    throw new Error('Could not extract product title');
  },

  extractSeller() {
    for (const selector of this.selectors.seller) {
      const seller = CommonExtractor.extractText(selector);
      if (seller && seller.length > 1 && !seller.toLowerCase().includes('amazon')) {
        return CommonExtractor.cleanText(seller);
      }
    }
    
    // Default to Amazon if no third-party seller found
    return 'Amazon';
  },

  extractImages() {
    const images = CommonExtractor.extractImages(this.selectors.images);
    
    // Amazon-specific image processing
    return images.map((image, index) => {
      let imageUrl = image.imageUrl;
      
      // Convert Amazon thumbnail URLs to full size
      if (imageUrl.includes('._AC_SX')) {
        imageUrl = imageUrl.replace(/\._AC_SX\d+_/, '._AC_SL1500_');
      } else if (imageUrl.includes('._AC_UX')) {
        imageUrl = imageUrl.replace(/\._AC_UX\d+_/, '._AC_SL1500_');
      } else if (imageUrl.includes('._SX')) {
        imageUrl = imageUrl.replace(/\._SX\d+_/, '._SL1500_');
      }
      
      return {
        ...image,
        imageUrl: imageUrl,
        isPrimary: index === 0
      };
    });
  },

  extractMetadata() {
    const metadata = {};
    
    // Extract price
    const price = this.extractPrice();
    if (price) {
      metadata.price = price.raw;
      metadata.currency = price.currency;
    }
    
    // Extract rating
    const rating = this.extractRating();
    if (rating) {
      metadata.rating = rating;
    }
    
    // Extract review count
    const reviewCount = this.extractReviewCount();
    if (reviewCount) {
      metadata.reviewCount = reviewCount;
    }
    
    // Extract availability
    const availability = this.extractAvailability();
    if (availability) {
      metadata.availability = availability;
    }
    
    // Extract description/features
    const description = this.extractDescription();
    if (description) {
      metadata.description = description;
    }
    
    return metadata;
  },

  extractPrice() {
    // Amazon has complex price structures
    for (const selector of this.selectors.price) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          let priceText = element.textContent.trim();
          
          // Handle screen reader text
          if (element.classList.contains('a-offscreen')) {
            priceText = element.textContent.trim();
          }
          
          if (priceText.match(/[\$€£¥]\s*[\d,]+\.?\d*/)) {
            return {
              raw: priceText,
              amount: priceText.match(/[\d,]+\.?\d*/)[0].replace(/,/g, ''),
              currency: CommonExtractor.extractCurrency(priceText)
            };
          }
        }
      } catch (error) {
        console.warn(`Failed to extract price from ${selector}:`, error);
      }
    }
    
    return null;
  },

  extractRating() {
    for (const selector of this.selectors.rating) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const altText = element.getAttribute('alt') || element.textContent;
          const ratingMatch = altText.match(/(\d+\.?\d*)\s*out\s*of\s*5/i);
          
          if (ratingMatch) {
            return ratingMatch[1];
          }
        }
      } catch (error) {
        console.warn(`Failed to extract rating from ${selector}:`, error);
      }
    }
    
    return null;
  },

  extractReviewCount() {
    for (const selector of this.selectors.reviewCount) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const text = element.textContent.trim();
          const match = text.match(/(\d+(?:,\d+)*)/);
          if (match) {
            return match[1];
          }
        }
      } catch (error) {
        console.warn(`Failed to extract review count from ${selector}:`, error);
      }
    }
    
    return null;
  },

  extractAvailability() {
    for (const selector of this.selectors.availability) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const text = element.textContent.trim().toLowerCase();
          
          if (text.includes('in stock') || text.includes('available')) {
            return 'In Stock';
          } else if (text.includes('out of stock') || text.includes('unavailable')) {
            return 'Out of Stock';
          } else if (text.includes('temporarily out of stock')) {
            return 'Temporarily Out of Stock';
          }
        }
      } catch (error) {
        console.warn(`Failed to extract availability from ${selector}:`, error);
      }
    }
    
    // Check if "Add to Cart" button exists
    const addToCartBtn = document.querySelector('#add-to-cart-button, [name="submit.add-to-cart"]');
    if (addToCartBtn && !addToCartBtn.disabled) {
      return 'In Stock';
    }
    
    return 'Unknown';
  },

  extractDescription() {
    // Try to extract bullet points first
    try {
      const bulletPoints = document.querySelectorAll('#feature-bullets ul .a-list-item');
      if (bulletPoints.length > 0) {
        const features = Array.from(bulletPoints)
          .map(item => item.textContent.trim())
          .filter(text => text.length > 5)
          .slice(0, 5) // Take first 5 features
          .join('. ');
        
        if (features) {
          return CommonExtractor.cleanText(features).substring(0, 500);
        }
      }
    } catch (error) {
      console.warn('Failed to extract bullet points:', error);
    }
    
    // Fallback to product description
    for (const selector of this.selectors.description) {
      const description = CommonExtractor.extractText(selector);
      if (description && description.length > 20) {
        return CommonExtractor.cleanText(description).substring(0, 500);
      }
    }
    
    return null;
  },

  // Check if current page is an Amazon product page
  isProductPage() {
    return (window.location.pathname.includes('/dp/') || 
            window.location.pathname.includes('/gp/product/')) && 
           window.location.hostname.includes('amazon.com');
  }
};

// Auto-extract when page loads (for manual crawling)
if (AmazonExtractor.isProductPage()) {
  // Wait for page to fully load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        console.log('Amazon product page detected and ready for extraction');
      }, 1000);
    });
  } else {
    console.log('Amazon product page detected and ready for extraction');
  }
}
