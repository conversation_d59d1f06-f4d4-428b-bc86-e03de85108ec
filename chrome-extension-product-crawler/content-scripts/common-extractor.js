// Common utilities for product data extraction

window.CommonExtractor = {
  // Wait for element to appear in DOM
  waitForElement(selector, timeout = 5000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver((mutations) => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  },

  // Extract text content safely
  extractText(selector, defaultValue = '') {
    try {
      const element = document.querySelector(selector);
      return element ? element.textContent.trim() : defaultValue;
    } catch (error) {
      console.warn(`Failed to extract text from ${selector}:`, error);
      return defaultValue;
    }
  },

  // Extract attribute value safely
  extractAttribute(selector, attribute, defaultValue = '') {
    try {
      const element = document.querySelector(selector);
      return element ? (element.getAttribute(attribute) || defaultValue) : defaultValue;
    } catch (error) {
      console.warn(`Failed to extract ${attribute} from ${selector}:`, error);
      return defaultValue;
    }
  },

  // Extract multiple elements
  extractMultiple(selector, extractor) {
    try {
      const elements = document.querySelectorAll(selector);
      return Array.from(elements).map(extractor).filter(Boolean);
    } catch (error) {
      console.warn(`Failed to extract multiple elements from ${selector}:`, error);
      return [];
    }
  },

  // Clean and normalize text
  cleanText(text) {
    if (!text) return '';
    return text
      .replace(/\s+/g, ' ')
      .replace(/\n+/g, ' ')
      .trim();
  },

  // Extract images with fallbacks
  extractImages(selectors) {
    const images = [];
    const seenUrls = new Set();

    for (const selector of selectors) {
      try {
        const elements = document.querySelectorAll(selector);
        
        elements.forEach((img, index) => {
          let imageUrl = img.src || img.getAttribute('data-src') || img.getAttribute('data-lazy-src');
          
          if (imageUrl && !seenUrls.has(imageUrl)) {
            // Convert relative URLs to absolute
            if (imageUrl.startsWith('//')) {
              imageUrl = window.location.protocol + imageUrl;
            } else if (imageUrl.startsWith('/')) {
              imageUrl = window.location.origin + imageUrl;
            }
            
            seenUrls.add(imageUrl);
            images.push({
              imageUrl: imageUrl,
              isPrimary: index === 0,
              sortOrder: images.length
            });
          }
        });
      } catch (error) {
        console.warn(`Failed to extract images from ${selector}:`, error);
      }
    }

    return images;
  },

  // Get current page URL
  getCurrentUrl() {
    return window.location.href;
  },

  // Detect marketplace from URL
  detectMarketplace() {
    const url = window.location.hostname.toLowerCase();
    
    if (url.includes('etsy.com')) return 'etsy';
    if (url.includes('ebay.com')) return 'ebay';
    if (url.includes('amazon.com')) return 'amazon';
    
    return 'unknown';
  },

  // Extract price information
  extractPrice(selectors) {
    for (const selector of selectors) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const priceText = element.textContent.trim();
          const priceMatch = priceText.match(/[\d,]+\.?\d*/);
          
          if (priceMatch) {
            return {
              raw: priceText,
              amount: priceMatch[0].replace(/,/g, ''),
              currency: this.extractCurrency(priceText)
            };
          }
        }
      } catch (error) {
        console.warn(`Failed to extract price from ${selector}:`, error);
      }
    }
    
    return null;
  },

  // Extract currency from price text
  extractCurrency(priceText) {
    if (priceText.includes('$')) return 'USD';
    if (priceText.includes('€')) return 'EUR';
    if (priceText.includes('£')) return 'GBP';
    if (priceText.includes('¥')) return 'JPY';
    
    return 'USD'; // Default
  },

  // Extract rating information
  extractRating(selectors) {
    for (const selector of selectors) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const ratingText = element.textContent.trim();
          const ratingMatch = ratingText.match(/(\d+\.?\d*)/);
          
          if (ratingMatch) {
            return ratingMatch[1];
          }
        }
      } catch (error) {
        console.warn(`Failed to extract rating from ${selector}:`, error);
      }
    }
    
    return null;
  },

  // Send extracted data to background script
  async sendToBackground(productData) {
    try {
      const response = await chrome.runtime.sendMessage({
        type: 'EXTRACT_PRODUCT',
        data: productData
      });
      
      return response;
    } catch (error) {
      console.error('Failed to send data to background:', error);
      throw error;
    }
  },

  // Show notification to user
  showNotification(message, type = 'info') {
    // Create a simple notification element
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
      color: white;
      padding: 12px 20px;
      border-radius: 4px;
      z-index: 10000;
      font-family: Arial, sans-serif;
      font-size: 14px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      max-width: 300px;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  },

  // Validate extracted product data
  validateProductData(productData) {
    const errors = [];
    
    if (!productData.title || productData.title.length < 3) {
      errors.push('Product title is required and must be at least 3 characters');
    }
    
    if (!productData.productUrl) {
      errors.push('Product URL is required');
    }
    
    if (!productData.marketplace) {
      errors.push('Marketplace is required');
    }
    
    if (!productData.images || productData.images.length === 0) {
      errors.push('At least one product image is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }
};
