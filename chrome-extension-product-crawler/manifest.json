{"manifest_version": 3, "name": "Product Crawler for TikTok Shop", "version": "1.0.0", "description": "Extract product information from e-commerce marketplaces for TikTok Shop integration", "permissions": ["storage", "activeTab", "scripting", "alarms", "tabs"], "host_permissions": ["https://www.etsy.com/*", "https://www.ebay.com/*", "https://www.amazon.com/*", "https://localhost:3000/*", "https://your-backend-domain.com/*"], "background": {"service_worker": "background/service-worker.js"}, "content_scripts": [{"matches": ["https://www.etsy.com/listing/*"], "js": ["content-scripts/common-extractor.js", "content-scripts/etsy-extractor.js"], "run_at": "document_idle"}, {"matches": ["https://www.ebay.com/itm/*"], "js": ["content-scripts/common-extractor.js", "content-scripts/ebay-extractor.js"], "run_at": "document_idle"}, {"matches": ["https://www.amazon.com/dp/*", "https://www.amazon.com/gp/product/*"], "js": ["content-scripts/common-extractor.js", "content-scripts/amazon-extractor.js"], "run_at": "document_idle"}], "action": {"default_popup": "popup/popup.html", "default_title": "Product Crawler", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["auth/*"], "matches": ["<all_urls>"]}]}