#!/bin/bash

# Setup script for creating Chrome Extension as separate repository
# This script helps organize the chrome-extension-product-crawler as a separate repository

echo "🚀 Setting up Chrome Extension as separate repository..."

# Check if chrome-extension-product-crawler directory exists
if [ ! -d "chrome-extension-product-crawler" ]; then
    echo "❌ Error: chrome-extension-product-crawler directory not found!"
    echo "Please make sure you're running this script from the main project directory."
    exit 1
fi

# Create a temporary directory for the new repository
TEMP_DIR="temp-chrome-extension"
FINAL_DIR="tts-chrome-extension"

echo "📁 Creating temporary directory..."
mkdir -p "$TEMP_DIR"

# Copy all files from chrome-extension-product-crawler to temp directory
echo "📋 Copying Chrome extension files..."
cp -r chrome-extension-product-crawler/* "$TEMP_DIR/"

# Create additional files for the new repository
echo "📝 Creating repository files..."

# Create package.json for the Chrome extension
cat > "$TEMP_DIR/package.json" << 'EOF'
{
  "name": "tts-chrome-extension",
  "version": "1.0.0",
  "description": "Chrome extension for extracting product information from e-commerce marketplaces and integrating with TikTok Shop",
  "main": "manifest.json",
  "scripts": {
    "build": "echo 'No build process required for Chrome extension'",
    "test": "echo 'No tests specified'",
    "lint": "echo 'No linting configured'"
  },
  "keywords": [
    "chrome-extension",
    "product-crawler",
    "tiktok-shop",
    "e-commerce",
    "etsy",
    "ebay",
    "amazon"
  ],
  "author": "TikTok Shop Team",
  "license": "MIT",
  "repository": {
    "type": "git",
    "url": "https://github.com/vinhWater/tts-chrome-extension.git"
  },
  "bugs": {
    "url": "https://github.com/vinhWater/tts-chrome-extension/issues"
  },
  "homepage": "https://github.com/vinhWater/tts-chrome-extension#readme"
}
EOF

# Create .gitignore for Chrome extension
cat > "$TEMP_DIR/.gitignore" << 'EOF'
# Chrome Extension specific
*.crx
*.pem
*.zip

# Development files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
EOF

# Create LICENSE file
cat > "$TEMP_DIR/LICENSE" << 'EOF'
MIT License

Copyright (c) 2024 TikTok Shop Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
EOF

echo "✅ Repository files created successfully!"

# Rename temp directory to final name
if [ -d "$FINAL_DIR" ]; then
    echo "⚠️  Warning: $FINAL_DIR already exists. Removing it..."
    rm -rf "$FINAL_DIR"
fi

mv "$TEMP_DIR" "$FINAL_DIR"

echo "📦 Chrome extension repository prepared as: $FINAL_DIR"

# Initialize git repository
cd "$FINAL_DIR"
echo "🔧 Initializing git repository..."
git init
git add .
git commit -m "Initial commit: Chrome extension for TikTok Shop product crawling

- Product extraction from Etsy, eBay, and Amazon
- Authentication with backend API
- Background service worker for automated tasks
- Popup interface for user interactions
- Content scripts for marketplace-specific extraction"

echo ""
echo "🎉 Chrome extension repository setup complete!"
echo ""
echo "Next steps:"
echo "1. Create a new repository on GitHub: https://github.com/vinhWater/tts-chrome-extension"
echo "2. Add the remote origin:"
echo "   git remote add origin https://github.com/vinhWater/tts-chrome-extension.git"
echo "3. Push to GitHub:"
echo "   git branch -M main"
echo "   git push -u origin main"
echo ""
echo "4. Update the main project to reference the new repository:"
echo "   cd .."
echo "   rm -rf chrome-extension-product-crawler"
echo "   git clone https://github.com/vinhWater/tts-chrome-extension.git"
echo ""
echo "Repository location: $(pwd)"
