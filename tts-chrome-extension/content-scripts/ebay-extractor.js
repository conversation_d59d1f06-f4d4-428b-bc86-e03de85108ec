// eBay-specific product data extractor

window.EbayExtractor = {
  // eBay-specific selectors
  selectors: {
    title: [
      '#x-title-label-lbl',
      '.x-item-title-label',
      'h1[data-testid="x-item-title-label"]',
      '.notranslate'
    ],
    images: [
      '#icImg',
      '.img img',
      '#mainImgHldr img',
      '.ux-image-carousel img',
      '.ux-image-filmstrip img'
    ],
    seller: [
      '.mbg-nw',
      '.seller-persona .mbg-nw',
      '[data-testid="seller-name"]',
      '.seller-info .mbg-nw'
    ],
    price: [
      '.notranslate',
      '.u-flL.condText',
      '[data-testid="price"]',
      '.display-price',
      '.notranslate[role="text"]'
    ],
    condition: [
      '.u-flL.condText',
      '[data-testid="condition"]',
      '.condition-text'
    ],
    shipping: [
      '.vi-price .vi-acc-del-range',
      '[data-testid="shipping-cost"]',
      '.shipping-cost'
    ],
    availability: [
      '#qtySubTxt',
      '[data-testid="quantity-available"]',
      '.quantity-available'
    ],
    description: [
      '#desc_div',
      '.item-description',
      '[data-testid="item-description"]'
    ]
  },

  // Extract product data from eBay listing page
  extractProductData() {
    try {
      console.log('Extracting eBay product data...');

      const productData = {
        title: this.extractTitle(),
        productUrl: CommonExtractor.getCurrentUrl(),
        marketplace: 'ebay',
        sellerName: this.extractSeller(),
        images: this.extractImages(),
        metadata: this.extractMetadata()
      };

      // Validate the extracted data
      const validation = CommonExtractor.validateProductData(productData);
      
      if (!validation.isValid) {
        console.error('eBay product data validation failed:', validation.errors);
        CommonExtractor.showNotification(
          'Failed to extract complete product data: ' + validation.errors.join(', '),
          'error'
        );
        return null;
      }

      console.log('eBay product data extracted successfully:', productData);
      return productData;

    } catch (error) {
      console.error('Error extracting eBay product data:', error);
      CommonExtractor.showNotification('Error extracting product data: ' + error.message, 'error');
      return null;
    }
  },

  extractTitle() {
    for (const selector of this.selectors.title) {
      const title = CommonExtractor.extractText(selector);
      if (title && title.length > 5) {
        return CommonExtractor.cleanText(title);
      }
    }
    
    // Fallback to page title
    const pageTitle = document.title;
    if (pageTitle && !pageTitle.includes('eBay')) {
      return CommonExtractor.cleanText(pageTitle.split('|')[0]);
    }
    
    throw new Error('Could not extract product title');
  },

  extractSeller() {
    for (const selector of this.selectors.seller) {
      const seller = CommonExtractor.extractText(selector);
      if (seller && seller.length > 1) {
        return CommonExtractor.cleanText(seller);
      }
    }
    
    return null;
  },

  extractImages() {
    const images = CommonExtractor.extractImages(this.selectors.images);
    
    // eBay-specific image processing
    return images.map((image, index) => {
      let imageUrl = image.imageUrl;
      
      // Convert eBay thumbnail URLs to full size
      if (imageUrl.includes('s-l64')) {
        imageUrl = imageUrl.replace('s-l64', 's-l1600');
      } else if (imageUrl.includes('s-l225')) {
        imageUrl = imageUrl.replace('s-l225', 's-l1600');
      } else if (imageUrl.includes('s-l300')) {
        imageUrl = imageUrl.replace('s-l300', 's-l1600');
      }
      
      return {
        ...image,
        imageUrl: imageUrl,
        isPrimary: index === 0
      };
    });
  },

  extractMetadata() {
    const metadata = {};
    
    // Extract price
    const price = this.extractPrice();
    if (price) {
      metadata.price = price.raw;
      metadata.currency = price.currency;
    }
    
    // Extract condition
    const condition = this.extractCondition();
    if (condition) {
      metadata.condition = condition;
    }
    
    // Extract shipping info
    const shipping = this.extractShipping();
    if (shipping) {
      metadata.shipping = shipping;
    }
    
    // Extract availability
    const availability = this.extractAvailability();
    if (availability) {
      metadata.availability = availability;
    }
    
    // Extract description
    const description = this.extractDescription();
    if (description) {
      metadata.description = description;
    }
    
    return metadata;
  },

  extractPrice() {
    // eBay has complex price structures, try multiple approaches
    const priceSelectors = [
      '.notranslate[role="text"]',
      '.display-price',
      '.u-flL.condText .notranslate',
      '.price .notranslate'
    ];
    
    for (const selector of priceSelectors) {
      try {
        const elements = document.querySelectorAll(selector);
        for (const element of elements) {
          const text = element.textContent.trim();
          if (text.match(/[\$€£¥]\s*[\d,]+\.?\d*/)) {
            return {
              raw: text,
              amount: text.match(/[\d,]+\.?\d*/)[0].replace(/,/g, ''),
              currency: CommonExtractor.extractCurrency(text)
            };
          }
        }
      } catch (error) {
        console.warn(`Failed to extract price from ${selector}:`, error);
      }
    }
    
    return null;
  },

  extractCondition() {
    for (const selector of this.selectors.condition) {
      const condition = CommonExtractor.extractText(selector);
      if (condition && condition.toLowerCase().includes('condition')) {
        return CommonExtractor.cleanText(condition);
      }
    }
    
    return null;
  },

  extractShipping() {
    for (const selector of this.selectors.shipping) {
      const shipping = CommonExtractor.extractText(selector);
      if (shipping && (shipping.includes('shipping') || shipping.includes('delivery'))) {
        return CommonExtractor.cleanText(shipping);
      }
    }
    
    return null;
  },

  extractAvailability() {
    for (const selector of this.selectors.availability) {
      try {
        const element = document.querySelector(selector);
        if (element) {
          const text = element.textContent.trim().toLowerCase();
          
          if (text.includes('available') || text.includes('in stock')) {
            return 'In Stock';
          } else if (text.includes('out of stock') || text.includes('sold out')) {
            return 'Out of Stock';
          } else if (text.match(/\d+\s*(available|left)/)) {
            return 'In Stock';
          }
        }
      } catch (error) {
        console.warn(`Failed to extract availability from ${selector}:`, error);
      }
    }
    
    // Check if "Add to cart" button exists and is enabled
    const addToCartBtn = document.querySelector('#atcBtn, [data-testid="add-to-cart"]');
    if (addToCartBtn && !addToCartBtn.disabled) {
      return 'In Stock';
    }
    
    return 'Unknown';
  },

  extractDescription() {
    for (const selector of this.selectors.description) {
      const description = CommonExtractor.extractText(selector);
      if (description && description.length > 20) {
        return CommonExtractor.cleanText(description).substring(0, 500);
      }
    }
    
    return null;
  },

  // Check if current page is an eBay product page
  isProductPage() {
    return (window.location.pathname.includes('/itm/') || 
            window.location.pathname.includes('/p/')) && 
           window.location.hostname.includes('ebay.com');
  }
};

// Auto-extract when page loads (for manual crawling)
if (EbayExtractor.isProductPage()) {
  // Wait for page to fully load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        console.log('eBay product page detected and ready for extraction');
      }, 1000);
    });
  } else {
    console.log('eBay product page detected and ready for extraction');
  }
}
