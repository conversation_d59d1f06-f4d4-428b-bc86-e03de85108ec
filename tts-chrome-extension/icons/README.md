# Extension Icons

This directory should contain the following icon files:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (extension management page)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Icon Requirements

- PNG format
- Transparent background
- Simple, recognizable design
- Consistent visual style across all sizes
- Should represent product crawling/extraction functionality

## Temporary Solution

For development purposes, you can use placeholder icons or create simple icons using any image editor. The icons should be related to:
- Shopping/e-commerce (shopping cart, package, etc.)
- Data extraction (download arrow, database, etc.)
- TikTok Shop integration (if appropriate)

## Production Icons

For production deployment, professional icons should be created that:
- Follow Chrome Web Store guidelines
- Are visually appealing and professional
- Clearly represent the extension's functionality
- Are optimized for different display densities
