{"name": "tts-chrome-extension", "version": "1.0.0", "description": "Chrome extension for extracting product information from e-commerce marketplaces and integrating with TikTok Shop", "main": "manifest.json", "scripts": {"build": "echo 'No build process required for Chrome extension'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'"}, "keywords": ["chrome-extension", "product-crawler", "tiktok-shop", "e-commerce", "etsy", "ebay", "amazon"], "author": "TikTok Shop Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/vinhWater/tts-chrome-extension.git"}, "bugs": {"url": "https://github.com/vinhWater/tts-chrome-extension/issues"}, "homepage": "https://github.com/vinhWater/tts-chrome-extension#readme"}