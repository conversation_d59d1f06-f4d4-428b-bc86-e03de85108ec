// Popup script for Product Crawler extension

class PopupManager {
  constructor() {
    this.currentTab = null;
    this.authStatus = null;
    this.init();
  }

  async init() {
    console.log('Initializing popup...');

    // Set up event listeners
    this.setupEventListeners();

    // Get current tab info
    await this.getCurrentTab();

    // Check authentication status
    await this.checkAuthStatus();

    // Update UI based on current state
    this.updateUI();

    // Load statistics if authenticated
    if (this.authStatus?.isAuthenticated) {
      await this.loadStatistics();
      await this.loadRecentActivity();
    }
  }

  setupEventListeners() {
    // Authentication buttons
    document.getElementById('login-btn').addEventListener('click', () => this.handleLogin());
    document.getElementById('logout-btn').addEventListener('click', () => this.handleLogout());

    // Extract button
    document.getElementById('extract-btn').addEventListener('click', () => this.handleExtract());

    // Action buttons
    document.getElementById('view-crawled-btn').addEventListener('click', () => this.openWebApp('/client/crawled-products'));
    document.getElementById('manage-schedules-btn').addEventListener('click', () => this.openWebApp('/client/crawl-schedules'));

    // Footer links
    document.getElementById('help-link').addEventListener('click', () => this.openWebApp('/help'));
    document.getElementById('settings-link').addEventListener('click', () => this.openWebApp('/settings'));

    // Message close buttons
    document.getElementById('error-close').addEventListener('click', () => this.hideMessage('error'));
    document.getElementById('success-close').addEventListener('click', () => this.hideMessage('success'));
  }

  async getCurrentTab() {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      this.currentTab = tab;
      console.log('Current tab:', tab);
    } catch (error) {
      console.error('Error getting current tab:', error);
    }
  }

  async checkAuthStatus() {
    try {
      this.showLoading(true);

      const response = await chrome.runtime.sendMessage({
        type: 'GET_AUTH_STATUS'
      });

      if (response.success) {
        this.authStatus = response.data;
        console.log('Auth status:', this.authStatus);
      } else {
        console.error('Failed to check auth status:', response.error);
        this.authStatus = { isAuthenticated: false };
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      this.authStatus = { isAuthenticated: false };
    } finally {
      this.showLoading(false);
    }
  }

  updateUI() {
    // Update authentication section
    this.updateAuthSection();

    // Update page section
    this.updatePageSection();

    // Show/hide sections based on auth status
    const sectionsToShow = this.authStatus?.isAuthenticated
      ? ['page-section', 'actions-section', 'stats-section', 'activity-section']
      : [];

    sectionsToShow.forEach(sectionId => {
      document.getElementById(sectionId).style.display = 'block';
    });
  }

  updateAuthSection() {
    const loginPrompt = document.getElementById('login-prompt');
    const userInfo = document.getElementById('user-info');

    if (this.authStatus?.isAuthenticated) {
      loginPrompt.style.display = 'none';
      userInfo.style.display = 'flex';

      const userName = this.authStatus.user?.email || 'User';
      document.getElementById('user-name').textContent = userName;
    } else {
      loginPrompt.style.display = 'block';
      userInfo.style.display = 'none';
    }
  }

  updatePageSection() {
    if (!this.currentTab) return;

    const marketplace = this.detectMarketplace(this.currentTab.url);
    const isProductPage = this.isProductPage(this.currentTab.url, marketplace);

    // Update marketplace badge
    const badge = document.getElementById('marketplace-badge');
    badge.textContent = marketplace;
    badge.className = `badge ${marketplace}`;

    // Update status text
    const statusText = document.getElementById('page-status-text');
    if (isProductPage) {
      statusText.textContent = 'Product page detected';
      document.getElementById('extract-section').style.display = 'block';
    } else if (marketplace !== 'unknown') {
      statusText.textContent = 'Navigate to a product page to extract';
      document.getElementById('extract-section').style.display = 'none';
    } else {
      statusText.textContent = 'Not on a supported marketplace';
      document.getElementById('extract-section').style.display = 'none';
    }
  }

  detectMarketplace(url) {
    if (!url) return 'unknown';

    if (url.includes('etsy.com')) return 'etsy';
    if (url.includes('ebay.com')) return 'ebay';
    if (url.includes('amazon.com')) return 'amazon';

    return 'unknown';
  }

  isProductPage(url, marketplace) {
    if (!url) return false;

    switch (marketplace) {
      case 'etsy':
        return url.includes('/listing/');
      case 'ebay':
        return url.includes('/itm/') || url.includes('/p/');
      case 'amazon':
        return url.includes('/dp/') || url.includes('/gp/product/');
      default:
        return false;
    }
  }

  async handleLogin() {
    try {
      this.showLoading(true);

      const response = await chrome.runtime.sendMessage({
        type: 'LOGIN'
      });

      if (response.success) {
        this.authStatus = response.data;
        this.updateUI();
        await this.loadStatistics();
        await this.loadRecentActivity();
        this.showMessage('Successfully logged in!', 'success');
      } else {
        this.showMessage('Login failed: ' + response.error, 'error');
      }
    } catch (error) {
      console.error('Login error:', error);
      this.showMessage('Login error: ' + error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  async handleLogout() {
    try {
      this.showLoading(true);

      const response = await chrome.runtime.sendMessage({
        type: 'LOGOUT'
      });

      if (response.success) {
        this.authStatus = { isAuthenticated: false };
        this.updateUI();
        this.showMessage('Successfully logged out!', 'success');
      } else {
        this.showMessage('Logout failed: ' + response.error, 'error');
      }
    } catch (error) {
      console.error('Logout error:', error);
      this.showMessage('Logout error: ' + error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  async handleExtract() {
    if (!this.authStatus?.isAuthenticated) {
      this.showMessage('Please log in first', 'error');
      return;
    }

    if (!this.currentTab) {
      this.showMessage('No active tab found', 'error');
      return;
    }

    const marketplace = this.detectMarketplace(this.currentTab.url);
    if (!this.isProductPage(this.currentTab.url, marketplace)) {
      this.showMessage('Please navigate to a product page first', 'error');
      return;
    }

    try {
      this.showLoading(true);
      this.updateExtractStatus('Extracting product data...', 'info');

      // Execute content script to extract product data
      const results = await chrome.scripting.executeScript({
        target: { tabId: this.currentTab.id },
        func: this.extractProductFromPage,
        args: [marketplace]
      });

      const productData = results[0].result;

      if (!productData) {
        throw new Error('Failed to extract product data');
      }

      this.updateExtractStatus('Saving to database...', 'info');

      // Send extracted data to background script
      const response = await chrome.runtime.sendMessage({
        type: 'EXTRACT_PRODUCT',
        data: productData
      });

      if (response.success) {
        this.updateExtractStatus('Product extracted successfully!', 'success');
        this.showMessage('Product extracted and saved!', 'success');

        // Refresh statistics
        await this.loadStatistics();
        await this.loadRecentActivity();
      } else {
        throw new Error(response.error);
      }

    } catch (error) {
      console.error('Extract error:', error);
      this.updateExtractStatus('Extraction failed', 'error');
      this.showMessage('Extraction failed: ' + error.message, 'error');
    } finally {
      this.showLoading(false);
    }
  }

  // Function to be injected into the page for extraction
  extractProductFromPage(marketplace) {
    let extractor;

    switch (marketplace) {
      case 'etsy':
        extractor = window.EtsyExtractor;
        break;
      case 'ebay':
        extractor = window.EbayExtractor;
        break;
      case 'amazon':
        extractor = window.AmazonExtractor;
        break;
      default:
        return null;
    }

    if (extractor && extractor.extractProductData) {
      return extractor.extractProductData();
    }

    return null;
  }

  async loadStatistics() {
    try {
      // This would normally fetch from the API
      // For now, we'll use placeholder data
      document.getElementById('total-products').textContent = '0';
      document.getElementById('active-schedules').textContent = '0';
    } catch (error) {
      console.error('Error loading statistics:', error);
    }
  }

  async loadRecentActivity() {
    try {
      const activityList = document.getElementById('recent-activity');
      activityList.innerHTML = '<div class="activity-item"><span class="activity-icon">📦</span><div class="activity-content"><div class="activity-title">No recent activity</div><div class="activity-time">Start extracting products to see activity</div></div></div>';
    } catch (error) {
      console.error('Error loading recent activity:', error);
    }
  }

  openWebApp(path) {
    const webAppUrl = 'http://localhost:3000' + path;
    chrome.tabs.create({ url: webAppUrl });
  }

  updateExtractStatus(message, type) {
    const statusElement = document.getElementById('extract-status');
    statusElement.textContent = message;
    statusElement.className = `status-message ${type}`;
  }

  showMessage(message, type) {
    const messageElement = document.getElementById(`${type}-message`);
    const textElement = document.getElementById(`${type}-text`);

    textElement.textContent = message;
    messageElement.style.display = 'flex';

    // Auto-hide after 5 seconds
    setTimeout(() => {
      this.hideMessage(type);
    }, 5000);
  }

  hideMessage(type) {
    const messageElement = document.getElementById(`${type}-message`);
    messageElement.style.display = 'none';
  }

  showLoading(show) {
    const loadingOverlay = document.getElementById('loading-overlay');
    loadingOverlay.style.display = show ? 'flex' : 'none';
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});
